#!/usr/bin/env python3
"""
Test script to demonstrate the validation fix for the calculator.

This shows how the enhanced validation now correctly distinguishes between:
- Input variables (must exist in LAS files)
- Output variables (being created by calculations)

Before the fix: "TEST = RHOB*2" would fail because TEST was treated as missing input
After the fix: "TEST = RHOB*2" passes because TEST is recognized as output variable
"""

import re

def test_old_validation_logic(calculation_text):
    """Simulate the old validation logic that caused the issue."""
    print("🔴 OLD VALIDATION LOGIC (BROKEN):")
    print(f"Calculation: {calculation_text}")
    
    # Old logic: Extract ALL variables without distinguishing input vs output
    variable_pattern = r'\b([A-Z_][A-Z0-9_]*)\b'
    variables = set(re.findall(variable_pattern, calculation_text.upper()))
    
    # Remove numpy functions
    numpy_functions = {'NP', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN', 'NANMIN', 'NANMAX', 'NANMEAN', 'NANSTD'}
    variables = variables - numpy_functions
    
    print(f"Variables found: {variables}")
    
    # Simulate available logs
    available_logs = {'DEPTH', 'RHOB', 'DT', 'GR', 'NPHI'}
    missing = variables - available_logs
    
    if missing:
        print(f"❌ VALIDATION FAILED: Missing variables: {missing}")
        print("❌ Would prevent user from proceeding with calculation")
        return False
    else:
        print("✅ Validation passed")
        return True

def test_new_validation_logic(calculation_text):
    """Simulate the new validation logic that fixes the issue."""
    print("\n🟢 NEW VALIDATION LOGIC (FIXED):")
    print(f"Calculation: {calculation_text}")
    
    # New logic: Parse to separate input vs output variables
    lines = [line.strip() for line in calculation_text.split('\n') if line.strip()]
    output_variables = set()
    input_variables = set()
    
    for line in lines:
        # Skip comments
        if line.startswith('#'):
            continue
            
        # Look for assignment operations
        if '=' in line and not any(op in line for op in ['==', '!=', '<=', '>=']):
            # Regular assignment
            parts = line.split('=', 1)
            if len(parts) == 2:
                left_side = parts[0].strip()
                right_side = parts[1].strip()
                
                # Extract output variable (left side)
                output_var = re.match(r'^([A-Z_][A-Z0-9_]*)', left_side.upper())
                if output_var:
                    output_variables.add(output_var.group(1))
                
                # Extract input variables (right side)
                right_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', right_side.upper()))
                input_variables.update(right_vars)
        else:
            # No assignment, treat all as input
            line_vars = set(re.findall(r'\b([A-Z_][A-Z0-9_]*)\b', line.upper()))
            input_variables.update(line_vars)
    
    # Remove numpy functions and output variables from input variables
    numpy_functions = {'NP', 'LOG', 'SQRT', 'EXP', 'SIN', 'COS', 'TAN', 'NANMIN', 'NANMAX', 'NANMEAN', 'NANSTD'}
    input_variables = input_variables - output_variables - numpy_functions
    
    print(f"Output variables (being created): {output_variables}")
    print(f"Input variables (must exist): {input_variables}")
    
    # Simulate available logs
    available_logs = {'DEPTH', 'RHOB', 'DT', 'GR', 'NPHI'}
    missing = input_variables - available_logs
    
    if missing:
        print(f"❌ VALIDATION FAILED: Missing INPUT variables: {missing}")
        return False
    else:
        print("✅ Validation passed - all INPUT variables are available")
        print(f"✅ Will create OUTPUT variables: {output_variables}")
        return True

def run_test_cases():
    """Run test cases to demonstrate the fix."""
    print("🧪 TESTING CALCULATOR VALIDATION FIX")
    print("=" * 60)
    
    test_cases = [
        "TEST = RHOB*2",
        "AI = RHOB * (304800/DT)",
        "VP_VS_RATIO = (304800/DT) / (304800/DTS)",  # This should fail (DTS missing)
        "NORMALIZED_GR = (GR - np.nanmin(GR)) / (np.nanmax(GR) - np.nanmin(GR))",
        "PHIE_HC = PHIE * (1 - SWE)",  # This should fail (PHIE, SWE missing)
    ]
    
    for i, calc in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST CASE {i}: {calc}")
        print('='*60)
        
        old_result = test_old_validation_logic(calc)
        new_result = test_new_validation_logic(calc)
        
        print(f"\n📊 COMPARISON:")
        print(f"Old logic result: {'✅ Pass' if old_result else '❌ Fail'}")
        print(f"New logic result: {'✅ Pass' if new_result else '❌ Fail'}")
        
        if old_result != new_result:
            if new_result:
                print("🎯 IMPROVEMENT: New logic correctly allows this calculation!")
            else:
                print("🔍 CHANGE: New logic is more strict about input requirements")
        else:
            print("🔄 SAME: Both logics agree on this case")

def demonstrate_specific_issue():
    """Demonstrate the specific issue reported by the user."""
    print("\n\n🎯 DEMONSTRATING THE SPECIFIC ISSUE")
    print("=" * 60)
    print("User reported: 'TEST = RHOB*2' fails validation")
    print("Expected: Should pass because TEST is output, RHOB is input")
    print()
    
    calculation = "TEST = RHOB*2"
    
    print("Before fix:")
    print("- Validation sees variables: {TEST, RHOB}")
    print("- Treats both as input variables that must exist")
    print("- TEST doesn't exist in LAS files → VALIDATION FAILS")
    print("- User can't proceed with calculation")
    
    print("\nAfter fix:")
    print("- Validation parses assignment: TEST = RHOB*2")
    print("- Identifies TEST as output variable (being created)")
    print("- Identifies RHOB as input variable (must exist)")
    print("- Only checks if RHOB exists → VALIDATION PASSES")
    print("- User can proceed with calculation")
    
    print(f"\n🧪 Testing with: {calculation}")
    test_new_validation_logic(calculation)

if __name__ == "__main__":
    run_test_cases()
    demonstrate_specific_issue()
    
    print("\n\n✅ CONCLUSION:")
    print("The validation fix correctly distinguishes between input and output variables.")
    print("Simple calculations like 'TEST = RHOB*2' now work as expected!")
    print("Users can create new calculated curves without validation errors.")
